#!/bin/bash

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "Flutter is not installed or not in PATH"
    echo "Please install Flutter from https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Get dependencies
echo "Getting dependencies..."
flutter pub get

# Run the app
echo "Running the app..."
flutter run

# If the app fails to run, try building it first
if [ $? -ne 0 ]; then
    echo "Failed to run the app, trying to build it first..."
    flutter build apk --debug
    flutter run
fi
