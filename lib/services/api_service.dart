import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';

class ApiService {
  final String baseUrl = 'https://api.example.com'; // Replace with actual API URL

  Future<List<String>> getBannerImages() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/banners'));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item['imageUrl'].toString()).toList();
      } else {
        throw Exception('Failed to load banner images');
      }
    } catch (e) {
      // Return local placeholder images for testing
      return [
        'assets/images/banner1.png',
        'assets/images/banner2.png',
        'assets/images/banner3.png',
      ];
    }
  }

  Future<ContactInfo> getContactInfo() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/contact'));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return ContactInfo.fromJson(data);
      } else {
        throw Exception('Failed to load contact information');
      }
    } catch (e) {
      // Return placeholder data for testing
      return ContactInfo(
        phone: '+966 123 456 789',
        email: '<EMAIL>',
        address: 'الرياض، المملكة العربية السعودية',
        facebookUrl: 'https://facebook.com',
        instagramUrl: 'https://instagram.com',
        twitterUrl: 'https://twitter.com',
      );
    }
  }

  Future<void> submitForm(
    int formType,
    String name,
    String phone,
    DateTime date,
    TimeOfDay time,
  ) async {
    final Map<String, dynamic> data = {
      'formType': formType,
      'name': name,
      'phone': phone,
      'date': '${date.year}-${date.month}-${date.day}',
      'time': '${time.hour}:${time.minute}',
    };

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/submit-form'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to submit form');
      }
    } catch (e) {
      // For testing, just print the data that would be sent
      print('Form data: $data');
      // In a real app, you would rethrow the exception
      // throw e;
    }
  }
}

class ContactInfo {
  final String phone;
  final String email;
  final String address;
  final String facebookUrl;
  final String instagramUrl;
  final String twitterUrl;

  ContactInfo({
    required this.phone,
    required this.email,
    required this.address,
    required this.facebookUrl,
    required this.instagramUrl,
    required this.twitterUrl,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phone: json['phone'],
      email: json['email'],
      address: json['address'],
      facebookUrl: json['facebook_url'],
      instagramUrl: json['instagram_url'],
      twitterUrl: json['twitter_url'],
    );
  }
}