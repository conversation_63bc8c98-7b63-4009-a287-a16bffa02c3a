import 'package:flutter/services.dart';
import 'dart:ui' as ui;

class CustomFontLoader {
  static Future<void> loadFonts() async {
    // Instead of manually loading fonts, let's just make sure they exist
    try {
      await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
      await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      print('Font files exist and can be loaded');
    } catch (e) {
      print('Error checking font files: $e');
    }
  }
}
