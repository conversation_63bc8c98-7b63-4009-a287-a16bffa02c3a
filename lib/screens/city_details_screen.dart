import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

class CityDetailsScreen extends StatefulWidget {
  final String cityName;
  
  const CityDetailsScreen({Key? key, required this.cityName}) : super(key: key);

  @override
  State<CityDetailsScreen> createState() => _CityDetailsScreenState();
}

class _CityDetailsScreenState extends State<CityDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dateController = TextEditingController(text: '5/20/2025');
  
  String _cityDescription = '';
  String _cityLocation = '';
  String _cityUnits = '';
  String _cityDistance = '';
  
  // Time picker variables
  int _selectedHour = 9;
  int _selectedMinute = 41;
  bool _isAM = true;
  
  @override
  void initState() {
    super.initState();
    _loadCityDetails();
  }
  
  void _loadCityDetails() {
    // This would be replaced with an actual API call
    switch (widget.cityName) {
      case 'مدينة الطيران':
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
        break;
      case 'جنوب سيناء':
        _cityDescription = 'مدينة جنوب سيناء السكنية';
        _cityLocation = 'تقع في الجهة الشرقية من مدينة الكويت';
        _cityUnits = 'على بعد 95 كيلومتر بعدد 35 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 110 كيلومتر';
        break;
      case 'جنوب صباح':
        _cityDescription = 'مدينة جنوب صباح السكنية';
        _cityLocation = 'تقع في الجهة الجنوبية من مدينة الكويت';
        _cityUnits = 'على بعد 80 كيلومتر بعدد 30 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 95 كيلومتر';
        break;
      case 'مدينة العبادية':
        _cityDescription = 'مدينة العبادية السكنية';
        _cityLocation = 'تقع في الجهة الشمالية من مدينة الكويت';
        _cityUnits = 'على بعد 120 كيلومتر بعدد 50 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 140 كيلومتر';
        break;
      case 'نواف الأحمد':
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
        break;
      default:
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _dateController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.grey),
          onPressed: () => context.go('/home'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, color: Colors.teal),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // City title
              Text(
                _cityDescription,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Color(0xFF9A7CB7),
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              
              // City description
              Text(
                _cityLocation,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),
              Text(
                _cityUnits,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),
              Text(
                _cityDistance,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 20),
              
              // Map placeholder
              Container(
                height: 180,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Icon(
                        Icons.map,
                        size: 80,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    Positioned(
                      bottom: 20,
                      right: 20,
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: const BoxDecoration(
                          color: Color(0xFF9A7CB7),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              
              // Registration form title
              Text(
                'سجل اهتمامك في وحدة بالمدينة',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 10),
              
              // Progress indicator
              Container(
                width: 120,
                height: 4,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9A7CB7), Color(0xFFB25A9E)],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              
              // Form
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Full Name
                    _buildFormField(
                      'الإسم الثلاثي',
                      Icons.person_outline,
                      TextFormField(
                        controller: _nameController,
                        decoration: _inputDecoration('الإسم الثلاثي'),
                        textAlign: TextAlign.right,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء إدخال الاسم الثلاثي';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 15),
                    
                    // Phone Number
                    _buildFormField(
                      'رقم الهاتف',
                      Icons.phone_outlined,
                      TextFormField(
                        controller: _phoneController,
                        decoration: _inputDecoration('0 0 0 0 0 0 0 0 0 0 0'),
                        keyboardType: TextInputType.phone,
                        textAlign: TextAlign.center,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء إدخال رقم الهاتف';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 15),
                    
                    // Housing Request Year
                    _buildFormField(
                      'سنة الطلب السكني',
                      Icons.calendar_today_outlined,
                      TextFormField(
                        controller: _dateController,
                        decoration: _inputDecoration(''),
                        keyboardType: TextInputType.datetime,
                        textAlign: TextAlign.center,
                        readOnly: true,
                        onTap: () {
                          // Show date picker
                          _showCustomDatePicker(context);
                        },
                      ),
                    ),
                    const SizedBox(height: 15),
                    
                    // Time picker
                    _buildTimePicker(),
                    
                    const SizedBox(height: 20),
                    
                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF9A7CB7),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${_selectedHour.toString().padLeft(2, '0')}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 5),
                            Text(
                              '${_selectedMinute.toString().padLeft(2, '0')}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 5),
                            Text(
                              _isAM ? 'AM' : 'PM',
                              style: const TextStyle(
                                fontSize: 18,
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildFormField(String label, IconData icon, Widget field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF9A7CB7),
                fontSize: 14,
                fontFamily: 'Cairo',
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              icon,
              size: 18,
              color: const Color(0xFF9A7CB7),
            ),
          ],
        ),
        const SizedBox(height: 8),
        field,
      ],
    );
  }
  
  InputDecoration _inputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      hintStyle: TextStyle(
        color: Colors.grey.shade400,
        fontSize: 14,
      ),
      filled: true,
      fillColor: Colors.grey.shade100,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
    );
  }
  
  Widget _buildTimePicker() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // AM/PM selector
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isAM = false;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: !_isAM ? const Color(0xFF9A7CB7) : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(10),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'PM',
                        style: TextStyle(
                          color: !_isAM ? Colors.white : Colors.grey,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isAM = true;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: _isAM ? const Color(0xFF9A7CB7) : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'AM',
                        style: TextStyle(
                          color: _isAM ? Colors.white : Colors.grey,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // Time slots
          Expanded(
            child: ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                final hour = 7 + index;
                final minute = 39 + index;
                final isSelected = hour == _selectedHour && minute == _selectedMinute;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedHour = hour;
                      _selectedMinute = minute;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFF9A7CB7) : Colors.transparent,
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Text(
                          _isAM ? 'AM' : 'PM',
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          minute.toString().padLeft(2, '0'),
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          hour.toString(),
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  void _showCustomDatePicker(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF9A7CB7),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
            dialogBackgroundColor: Colors.white,
          ),
          child: child!,
        );
      },
    ).then((selectedDate) {
      if (selectedDate != null) {
        setState(() {
          _dateController.text = '${selectedDate.month}/${selectedDate.day}/${selectedDate.year}';
        });
      }
    });
  }
  
  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Submit form data
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إرسال طلبك بنجاح')),
      );

      // Navigate back immediately
      context.go('/home');
    }
  }
}
