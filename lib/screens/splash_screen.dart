import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/logo_widget.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Navigate to home screen after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      context.go('/home');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF5F0F5), // Light lavender at top
              Color(0xFFEDE7ED), // Slightly darker lavender at bottom
            ],
          ),
        ),
        child: Stack(
          children: [
            // Background decorative dots
            Positioned(
              top: 120,
              left: 40,
              child: _buildDot(const Color(0xFFE67E51), 8),
            ),
            Positioned(
              top: 250,
              right: 60,
              child: _buildDot(const Color(0xFFB25A9E), 8),
            ),
            Positioned(
              bottom: 180,
              right: 50,
              child: _buildDot(const Color(0xFF30B4C3), 8),
            ),
            Positioned(
              bottom: 80,
              left: 30,
              child: _buildDot(const Color(0xFF9A7CB7), 8),
            ),
            // Additional decorative dots for more visual interest
            Positioned(
              top: 180,
              left: 80,
              child: _buildDot(const Color(0xFF30B4C3), 6),
            ),
            Positioned(
              bottom: 300,
              left: 20,
              child: _buildDot(const Color(0xFFE67E51), 6),
            ),

            // Main content
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LogoWidget(size: 200),
                  SizedBox(height: 20),
                    SizedBox(height: 30),
                  SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      color: Color(0xFF30B4C3),
                      strokeWidth: 3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDot(Color color, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildColorBar(Color color, double width) {
    return Container(
      width: width,
      height: 4,
      color: color,
    );
  }
}
