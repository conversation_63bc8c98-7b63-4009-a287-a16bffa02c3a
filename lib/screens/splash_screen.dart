import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/logo_widget.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Navigate to home screen after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      context.go('/home');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Background dots
          Positioned(
            top: 100,
            left: 50,
            child: _buildDot(const Color(0xFFE67E51), 10),
          ),
          Positioned(
            top: 200,
            right: 70,
            child: _buildDot(const Color(0xFFB25A9E), 10),
          ),
          Positioned(
            bottom: 100,
            right: 40,
            child: _buildDot(const Color(0xFF30B4C3), 10),
          ),
          Positioned(
            bottom: 150,
            left: 60,
            child: _buildDot(const Color(0xFF9A7CB7), 10),
          ),

          // Main content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const LogoWidget(size: 200),
                const SizedBox(height: 20),
                Image.asset(
                  'assets/images/text/image.png',
                  width: 250,
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 20),
                // Colorful line
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildColorBar(const Color(0xFF30B4C3), 20),
                    _buildColorBar(const Color(0xFFE67E51), 20),
                    _buildColorBar(const Color(0xFF5B6EAD), 20),
                    _buildColorBar(const Color(0xFFB25A9E), 20),
                  ],
                ),
                const SizedBox(height: 30),
                const SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    color: Color(0xFF30B4C3),
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(Color color, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildColorBar(Color color, double width) {
    return Container(
      width: width,
      height: 4,
      color: color,
    );
  }
}