import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ContactScreen extends StatelessWidget {
  const ContactScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.grey),
          onPressed: () => context.go('/home'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, color: Colors.teal),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Welcome text
              const Text(
                'نرحب بتواصلكم',
                style: TextStyle(
                  color: Color(0xFF5B6EAD),
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 5),
              const Text(
                'اختر وسيلة التواصل المناسبة لك',
                style: TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 20),

              // Illustration
              Stack(
                alignment: Alignment.center,
                children: [
                  // Background
                  Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),

                  // Person illustration
                  Image.asset(
                    'assets/images/image copy 3.png',
                    height: 180,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback if image is not available
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.person,
                            size: 80,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildCommunicationIcon(
                                  Icons.phone, Colors.purple),
                              const SizedBox(width: 15),
                              _buildCommunicationIcon(
                                  Icons.chat_bubble, Colors.orange),
                              const SizedBox(width: 15),
                              _buildCommunicationIcon(Icons.email, Colors.blue),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Divider
              Container(
                width: 120,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFF5B6EAD),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 20),

              // Phone contact button
              _buildContactButton(
                icon: Icons.phone,
                text: 'للتواصل المباشر / 98984798',
                onTap: () => _launchPhone('98984798'),
                color: Colors.grey.shade200,
                textColor: Colors.black87,
              ),

              const SizedBox(height: 15),

              // WhatsApp button
              _buildContactButton(
                icon: FontAwesomeIcons.whatsapp,
                text: 'لحجز مساحة إعلانية عبر الواتس',
                onTap: () => _launchWhatsApp('98984798'),
                color: const Color(0xFF5B6EAD),
                textColor: Colors.white,
              ),

              const SizedBox(height: 15),

              // Email button
              _buildContactButton(
                icon: Icons.email,
                text: 'للشكاوي والاقتراحات عبر الايميل',
                onTap: () => _launchEmail('<EMAIL>'),
                color: const Color(0xFF5B6EAD),
                textColor: Colors.white,
              ),

              const SizedBox(height: 30),

              // Social media text
              const Text(
                'تابعونا على منصات التواصل الاجتماعي',
                style: TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  fontFamily: 'Cairo',
                ),
              ),

              const SizedBox(height: 15),

              // Social media icons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildSocialIcon(
                      FontAwesomeIcons.xTwitter, 'https://twitter.com'),
                  const SizedBox(width: 15),
                  _buildSocialIcon(
                      FontAwesomeIcons.snapchat, 'https://snapchat.com'),
                  const SizedBox(width: 15),
                  _buildSocialIcon(
                      FontAwesomeIcons.tiktok, 'https://tiktok.com'),
                  const SizedBox(width: 15),
                  _buildSocialIcon(
                      FontAwesomeIcons.instagram, 'https://instagram.com'),
                  const SizedBox(width: 15),
                  _buildSocialIcon(
                      FontAwesomeIcons.whatsapp, 'https://wa.me/98984798'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommunicationIcon(IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  Widget _buildContactButton({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
    required Color color,
    required Color textColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(30),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              text,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontFamily: 'Cairo',
              ),
            ),
            const SizedBox(width: 10),
            Icon(
              icon,
              color: textColor,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialIcon(IconData icon, String url) {
    return InkWell(
      onTap: () {
        final Uri uri = Uri.parse(url);
        url_launcher.launchUrl(uri);
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: const Color(0xFF5B6EAD),
          size: 20,
        ),
      ),
    );
  }

  void _launchPhone(String phone) async {
    final Uri uri = Uri.parse('tel:$phone');
    if (await url_launcher.canLaunchUrl(uri)) {
      await url_launcher.launchUrl(uri);
    }
  }

  void _launchWhatsApp(String phone) async {
    final Uri uri = Uri.parse('https://wa.me/$phone');
    if (await url_launcher.canLaunchUrl(uri)) {
      await url_launcher.launchUrl(uri);
    }
  }

  void _launchEmail(String email) async {
    final Uri uri = Uri.parse('mailto:$email');
    if (await url_launcher.canLaunchUrl(uri)) {
      await url_launcher.launchUrl(uri);
    }
  }
}
