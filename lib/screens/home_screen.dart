import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/slideshow.dart';
import '../widgets/logo_widget.dart';
import '../services/api_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ApiService _apiService = ApiService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Phone button
            IconButton(
              icon: const Icon(Icons.phone, color: Colors.grey),
              onPressed: () {
                context.go('/contact');
              },
            ),
            // Notification button next to phone button
            IconButton(
              icon: const Icon(Icons.notifications_none, color: Colors.grey),
              onPressed: () {
                // Notifications action
              },
            ),
          ],
        ),
        leadingWidth: 120, // Give enough space for both buttons
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Logo and welcome message
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo with dashed circle
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer dashed circle
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey.shade400,
                            width: 1,
                            style: BorderStyle.solid,
                          ),
                        ),
                      ),
                      // Inner dashed circle
                      Container(
                        width: 90,
                        height: 90,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey.shade400,
                            width: 1,
                            style: BorderStyle.solid,
                          ),
                        ),
                      ),
                      // Logo
                      const LogoWidget(size: 80),
                    ],
                  ),
                  const SizedBox(height: 15),
                  // Text image after logo
                  Image.asset(
                    'assets/images/text/image copy 2.png',
                    width: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 10),
                  // Rainbow separation bar
                  _buildRainbowBar(),
                  const SizedBox(height: 10),
                  // Welcome text
                  Image.asset(
                    'assets/images/text/image copy 5.png',
                    width: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 10),

                  const SizedBox(height: 15),
                  // Additional text image
                  Image.asset(
                    'assets/images/test image copy 5.png',
                    height: 50,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return const SizedBox(
                          height: 10); // Fallback if image doesn't exist
                    },
                  ),
                ],
              ),
            ),

            // Empty space where slideshow would be
            const SizedBox(height: 20),

            // Current Cities Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Center(
                    child: Text(
                      'المدن الحالية',
                      style: TextStyle(
                        color: Color(0xFF30B4C3),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Zahra',
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNewCityCard(
                          'جنوب',
                          'سعد',
                          const Color(0xFF5B6EAD), // Blue gradient
                          'assets/images/text/image copy.png',
                          true, // isLargeSize for 2-city section
                          () => _navigateToCityDetails('جنوب سعد'),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: _buildNewCityCard(
                          'جنوب',
                          'صباح',
                          const Color(0xFF30B4C3), // Teal gradient
                          'assets/images/text/image.png',
                          true, // isLargeSize for 2-city section
                          () => _navigateToCityDetails('جنوب صباح'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),
            // Colorful line
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildColorBar(const Color(0xFF30B4C3), 20),
                _buildColorBar(const Color(0xFFE67E51), 20),
                _buildColorBar(const Color(0xFF5B6EAD), 20),
                _buildColorBar(const Color(0xFFB25A9E), 20),
              ],
            ),

            // Upcoming Cities Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Center(
                    child: Text(
                      'المدن القادمة',
                      style: TextStyle(
                        color: Color(0xFF30B4C3),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Zahra',
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNewCityCard(
                          'مدينة',
                          'الطيران',
                          const Color(0xFFB25A9E), // Pink gradient
                          'assets/images/text/image copy 3.png',
                          false, // isLargeSize for 3-city section
                          () => _navigateToCityDetails('مدينة الطيران'),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: _buildNewCityCard(
                          'نواف',
                          'الأحمد',
                          const Color(0xFF8C6DB0), // Purple gradient
                          'assets/images/text/image copy 5.png',
                          false, // isLargeSize for 3-city section
                          () => _navigateToCityDetails('نواف الأحمد'),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: _buildNewCityCard(
                          'مدينة',
                          'العبادية',
                          const Color(0xFFE67E51), // Orange gradient
                          'assets/images/text/image copy 4.png',
                          false, // isLargeSize for 3-city section
                          () => _navigateToCityDetails('مدينة العبادية'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildColorBar(Color color, double width) {
    return Container(
      width: width,
      height: 4,
      color: color,
    );
  }

  Widget _buildRainbowBar() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 14,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFFC476B0), // Pink
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Container(
          width: 14,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFF8B6DAF), // Purple
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Container(
          width: 14,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFF6C85BE), // Blue
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Container(
          width: 14,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFFDF815F), // Orange
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Container(
          width: 14,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFF0BACBC), // Teal
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    );
  }

  Widget _buildNewCityCard(String line1, String line2, Color color,
      String imagePath, bool isLargeSize, VoidCallback onTap) {
    // Define gradient based on the color
    Gradient gradient;

    // Based on the image, assign the correct gradient
    if (color == const Color(0xFF30B4C3)) {
      // Teal gradient (for جنوب سعد)
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF59D2D7), Color(0xFF0BACBC)],
      );
    } else if (color == const Color(0xFF5B6EAD)) {
      // Blue gradient (for جنوب صباح)
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF96C5EF), Color(0xFF6E81C6)],
      );
    } else if (color == const Color(0xFFE67E51)) {
      // Orange/Peach gradient (for مدينة الطيران)
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFF9A77B), Color(0xFFE67E51)],
      );
    } else if (color == const Color(0xFF8C6DB0)) {
      // Purple gradient (for نواف الأحمد)
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFB89DD7), Color(0xFF8C6DB0)],
      );
    } else if (color == const Color(0xFFB25A9E)) {
      // Pink gradient (for مدينة الطيران in second row)
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFE6A6D6), Color(0xFFC577B1)],
      );
    } else {
      // Default gradient
      gradient = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [color.withOpacity(0.7), color],
      );
    }

    return InkWell(
      onTap: onTap,
      child: AspectRatio(
        aspectRatio: 1,
        child: Container(
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  imagePath,
                  width: isLargeSize ? 150 : 120,
                  height: isLargeSize ? 75 : 60,
                  fit: BoxFit.contain,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToCityDetails(String cityName) {
    // Navigate to city details screen
    context.go('/city/${Uri.encodeComponent(cityName)}');
  }
}
