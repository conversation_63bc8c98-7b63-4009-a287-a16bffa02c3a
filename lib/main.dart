import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'screens/contact_screen.dart';
import 'screens/city_details_screen.dart';
import 'utils/font_loader.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await CustomFontLoader.loadFonts();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({Key? key}) : super(key: key);

  final _router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/contact',
        builder: (context, state) => const ContactScreen(),
      ),
      GoRoute(
        path: '/city/:name',
        builder: (context, state) => CityDetailsScreen(
          cityName: state.pathParameters['name'] ?? '',
        ),
      ),
    ],
  );

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'بيت العمر',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF30B4C3),
        colorScheme: ColorScheme.fromSwatch().copyWith(
          primary: const Color(0xFF30B4C3),
          secondary: const Color(0xFFB25A9E),
        ),
        fontFamily: 'Cairo', // Arabic font
        textTheme: Typography.material2018(platform: TargetPlatform.android)
            .black
            .apply(fontFamily: 'Cairo'),
      ),
      routerConfig: _router,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // Arabic
      ],
      locale: const Locale('ar', ''),
    );
  }
}
