import 'package:flutter/material.dart';
import 'placeholder_banner.dart';

class SlideshowBanner extends StatefulWidget {
  final List<String> images;

  const SlideshowBanner({Key? key, required this.images}) : super(key: key);

  @override
  State<SlideshowBanner> createState() => _SlideshowBannerState();
}

class _SlideshowBannerState extends State<SlideshowBanner> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();

  // Placeholder banners with different colors
  final List<Widget> _placeholderBanners = [
    const PlaceholderBanner(text: 'بيت العمر', color: Color(0xFF30B4C3)),
    const PlaceholderBanner(text: 'أكبر منصة للمقبلين على البناء', color: Color(0xFFE67E51)),
    const PlaceholderBanner(text: 'تصفح المدن الجديدة', color: Color(0xFFB25A9E)),
  ];

  @override
  void initState() {
    super.initState();
    // Auto-play functionality
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _autoPlaySlides();
      }
    });
  }

  void _autoPlaySlides() {
    if (!mounted) return;

    if (_currentIndex < _placeholderBanners.length - 1) {
      _currentIndex++;
    } else {
      _currentIndex = 0;
    }

    if (_pageController.hasClients) {
      _pageController.animateToPage(
        _currentIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    }

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _autoPlaySlides();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: Stack(
        children: [
          // Page View for slides
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: _placeholderBanners.length,
            itemBuilder: (context, index) {
              return Container(
                width: MediaQuery.of(context).size.width,
                margin: const EdgeInsets.symmetric(horizontal: 5.0),
                child: _placeholderBanners[index],
              );
            },
          ),

          // Indicators
          Positioned(
            bottom: 10,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _placeholderBanners.asMap().entries.map((entry) {
                return Container(
                  width: 8.0,
                  height: 8.0,
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == entry.key
                        ? const Color(0xFF30B4C3)
                        : Colors.white.withOpacity(0.5),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}