import 'package:flutter/material.dart';

class PlaceholderBanner extends StatelessWidget {
  final String text;
  final Color color;
  
  const PlaceholderBanner({
    Key? key,
    required this.text,
    this.color = const Color(0xFF30B4C3),
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }
}
