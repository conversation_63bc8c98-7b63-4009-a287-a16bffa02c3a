import 'package:flutter/material.dart';

class LogoWidget extends StatelessWidget {
  final double size;
  final bool showCircle;

  const LogoWidget({Key? key, this.size = 200, this.showCircle = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget logoContent = Image.asset(
      'assets/images/logo.png',
      width: size,
      height: size,
      fit: BoxFit.contain,
    );

    if (showCircle) {
      return Container(
        width: size,
        height: size,
        padding: EdgeInsets.all(size * 0.1),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.grey.withOpacity(0.5),
            width: 1,
            style: BorderStyle.solid,
          ),
        ),
        child: Center(child: logoContent),
      );
    }

    return SizedBox(
      width: size,
      height: size,
      child: Center(child: logoContent),
    );
  }
}
