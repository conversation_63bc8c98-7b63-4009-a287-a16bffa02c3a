import 'package:flutter/material.dart';

class TextImageWidget extends StatelessWidget {
  final String imagePath;
  final double width;
  final double height;
  
  const TextImageWidget({
    Key? key,
    required this.imagePath,
    this.width = 200,
    this.height = 50,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: BoxFit.contain,
    );
  }
}
