import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../widgets/request_form.dart';
import '../external_apps/app_integration.dart';

enum TabType {
  app1,
  app2,
  requestForm1,
  requestForm2,
  requestForm3,
}

class TabContent extends StatelessWidget {
  final TabType tabType;

  const TabContent({Key? key, required this.tabType}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (tabType) {
      case TabType.app1:
        return AppIntegration.launchApp1(); // Current cities app
      case TabType.app2:
        return AppIntegration.launchApp2(); // Future cities app
      case TabType.requestForm1:
        return RequestForm(formType: 1);
      case TabType.requestForm2:
        return RequestForm(formType: 2);
      case TabType.requestForm3:
        return RequestForm(formType: 3);
    }
  }
}