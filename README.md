# بيت العمر - Flutter App

A Flutter application with Arabic (RTL) support that integrates two existing Flutter apps and includes API integration for forms and slideshow.

## Project Structure

The application consists of the following screens:

### Splash Screen
- Displays the app logo while loading

### Homepage
- Includes a slideshow/banner at the top (data via API)
- Features 5 tabs:
  - Tab 1 & Tab 2: Each launches one of the provided Flutter apps (integration required)
  - Tab 3, Tab 4 & Tab 5: Each opens a request form (submits data via provided API)

### Contact Us Screen
- Includes contact information retrieved from API

## Setup Instructions

1. Make sure you have Flutter installed on your machine
2. Clone this repository
3. Run `flutter pub get` to install dependencies
4. Place the source code for the two external Flutter apps in the appropriate directories
5. Update the API endpoints in `lib/services/api_service.dart`
6. Run the app with `flutter run`

## External App Integration

To integrate the external Flutter apps:

1. Place the source code for the first app in `lib/external_apps/app1/`
2. Place the source code for the second app in `lib/external_apps/app2/`
3. Update the `AppIntegration` class in `lib/external_apps/app_integration.dart` to properly launch these apps

## API Integration

The app uses the following API endpoints:

- `/banners` - For slideshow images
- `/contact` - For contact information
- `/submit-form` - For form submission

Update the base URL and endpoints in `lib/services/api_service.dart` to match your API.

## RTL Support

The app is configured for Arabic (RTL) language support using:

- The Cairo font family
- Flutter's built-in RTL support
- Localization delegates for Arabic

## Dependencies

- flutter_localizations - For Arabic language support
- go_router - For navigation
- http - For API requests
- carousel_slider - For the slideshow
- url_launcher - For opening URLs
- intl - For internationalization

## Development

To run the app in development mode:

```bash
flutter run
```

To build a release version:

```bash
flutter build apk --release  # For Android
flutter build ios --release  # For iOS
```
